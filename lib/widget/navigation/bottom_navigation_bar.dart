import 'package:flutter/material.dart';

enum BottomNavTab {
  chats(0, 'Chats', Icons.chat_bubble_outline),
  contacts(1, 'Contacts', Icons.person_outline),
  people(2, 'People', Icons.search),
  settings(3, 'Setting', Icons.settings_outlined);

  const BottomNavTab(this.tabIndex, this.label, this.icon);

  final int tabIndex;
  final String label;
  final IconData icon;
}

class CustomBottomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 92,
      child: Stack(
        children: [
          // Background with subtle transparency (no blur to avoid making content blurry)
          Positioned(
            left: 0,
            top: 17,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white
                    .withValues(alpha: 0.95), // More opaque to maintain clarity
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.04),
                    offset: const Offset(0, -18),
                    blurRadius: 29,
                    spreadRadius: 0,
                  ),
                ],
              ),
            ),
          ),

          // Navigation items
          Positioned(
            left: 0,
            top: 17,
            right: 0,
            bottom: 0,
            child: Container(
              height: 75,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: BottomNavTab.values.map((tab) {
                        return _buildNavItem(context, tab);
                      }).toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Active indicator dot
          if (currentIndex >= 0 && currentIndex < BottomNavTab.values.length)
            Positioned(
              left: _getIndicatorPosition(),
              top: 10,
              child: Container(
                width: 13,
                height: 13,
                decoration: BoxDecoration(
                  color: const Color(0xFF6C4EFF), // Primary blue
                  borderRadius: BorderRadius.circular(34),
                ),
              ),
            ),
        ],
      ),
    );
  }

  double _getIndicatorPosition() {
    // Calculate the position of the indicator dot based on the current tab
    // Total width is 390px (from Figma), with 16px padding on each side = 358px usable
    // 4 tabs evenly spaced, so each tab gets 358/4 = 89.5px
    // The indicator should be centered above each tab
    const double totalWidth = 390;
    const double horizontalPadding = 16;
    const double usableWidth = totalWidth - (horizontalPadding * 2);
    const double tabWidth = usableWidth / 4;
    const double indicatorWidth = 13;

    // Calculate the center position for the current tab
    final double tabCenterX =
        horizontalPadding + (currentIndex * tabWidth) + (tabWidth / 2);

    // Adjust for the indicator width to center it
    return tabCenterX - (indicatorWidth / 2);
  }

  Widget _buildNavItem(BuildContext context, BottomNavTab tab) {
    final isActive = currentIndex == tab.tabIndex;

    return GestureDetector(
      onTap: () => onTap(tab.tabIndex),
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12.5),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon container
            SizedBox(
              width: 24,
              height: 24,
              child: _buildIcon(context, tab, isActive),
            ),
            const SizedBox(height: 5),
            // Label
            Text(
              tab.label,
              style: const TextStyle(
                fontFamily: 'SF Pro Text',
                fontSize: 12,
                fontWeight: FontWeight.w400,
                height: 1.193359375,
              ).copyWith(
                color: isActive
                    ? const Color(0xFF6C4EFF) // Primary blue
                    : const Color(0xFFAFAFAF), // Neutral 300 (inactive)
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIcon(BuildContext context, BottomNavTab tab, bool isActive) {
    final color = isActive
        ? const Color(0xFF6C4EFF) // Primary blue
        : const Color(0xFFAFAFAF); // Neutral 300 (inactive)

    // For the chat tab, we'll use a custom icon to match the design
    if (tab == BottomNavTab.chats) {
      return Icon(
        isActive ? Icons.chat_bubble : Icons.chat_bubble_outline,
        size: 24,
        color: color,
      );
    }

    return Icon(
      tab.icon,
      size: 24,
      color: color,
    );
  }
}

// Home indicator widget to match the Figma design
class HomeIndicator extends StatelessWidget {
  const HomeIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 34,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.95), // More opaque, no blur
      ),
      child: Center(
        child: Container(
          width: 148,
          height: 5,
          decoration: BoxDecoration(
            color: const Color(0xFF090A0A), // Ink/Darkest
            borderRadius: BorderRadius.circular(100),
          ),
        ),
      ),
    );
  }
}
